# API Structure Documentation

This directory contains the API layer for the frontend application. It provides a centralized way to manage all API calls with proper configuration and error handling.

## Structure

```
app/api/
├── index.ts              # Main export file
├── auth/
│   └── authApi.ts       # Authentication related APIs
├── events/
│   └── eventApi.ts      # Events related APIs
└── README.md            # This file

lib/
├── globalurl.ts         # Global URL configuration
└── customaxios.ts       # Custom axios instance with interceptors
```

## Usage

### 1. Import APIs in your components

```typescript
// Import specific API modules
import { authApi } from '@/app/api/auth/authApi';
import { eventApi } from '@/app/api/events/eventApi';

// Or import everything from the main index
import { authApi, eventApi, apiClient } from '@/app/api';
```

### 2. Use in components

```typescript
// Example: Login component
const handleLogin = async (email: string, password: string) => {
  try {
    const response = await authApi.login({ email, password });
    localStorage.setItem('authToken', response.token);
    // Handle success
  } catch (error) {
    // Handle error
    console.error('Login failed:', error);
  }
};

// Example: Events list component
const fetchEvents = async () => {
  try {
    const response = await eventApi.getEvents(1, 10);
    setEvents(response.events);
  } catch (error) {
    console.error('Failed to fetch events:', error);
  }
};
```

### 3. Direct axios usage

```typescript
import { apiClient, buildApiUrl } from '@/app/api';

// Direct API call
const customApiCall = async () => {
  try {
    const response = await apiClient.get(buildApiUrl('/custom-endpoint'));
    return response.data;
  } catch (error) {
    console.error('API call failed:', error);
  }
};
```

## Adding New API Modules

1. Create a new folder under `app/api/` (e.g., `users/`)
2. Create the API file (e.g., `userApi.ts`)
3. Define types and API functions
4. Export from the main `index.ts` file

### Example: Creating Users API

```typescript
// app/api/users/userApi.ts
import { apiClient } from '../../../lib/customaxios';
import { buildApiUrl } from '../../../lib/globalurl';

export interface User {
  id: string;
  name: string;
  email: string;
}

export const userApi = {
  getUsers: async (): Promise<User[]> => {
    const response = await apiClient.get<User[]>(buildApiUrl('/users'));
    return response.data;
  },
  
  getUserById: async (id: string): Promise<User> => {
    const response = await apiClient.get<User>(buildApiUrl(`/users/${id}`));
    return response.data;
  },
};
```

Then add to `app/api/index.ts`:
```typescript
export * from './users/userApi';
```

## Configuration

### Environment Variables

Make sure to set the `BASE_URL` in your `.env` file:
```
BASE_URL=http://localhost:8005
```

### Global Configuration

Modify `lib/globalurl.ts` to add new endpoints or change configuration:

```typescript
export const API_ENDPOINTS = {
  AUTH: '/auth',
  USERS: '/users',
  EVENTS: '/events',
  // Add more endpoints here
} as const;
```

## Features

- ✅ Centralized axios configuration
- ✅ Request/Response interceptors
- ✅ Automatic error handling
- ✅ Authentication token management
- ✅ Development logging
- ✅ TypeScript support
- ✅ Modular API structure
- ✅ Environment-based configuration

## Error Handling

The custom axios instance automatically handles common HTTP errors:
- 401: Unauthorized (can redirect to login)
- 403: Forbidden access
- 404: Resource not found
- 500: Server error
- Network errors

You can customize error handling in `lib/customaxios.ts`.
