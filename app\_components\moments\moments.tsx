"use client";
import React, { useState } from 'react';
import Image from 'next/image';
import { Carousel, CarouselContent, CarouselItem, CarouselPrevious, CarouselNext } from "@/components/ui/carousel";
import { ArrowRight } from 'lucide-react';
import titlearrow from "@/public/image/icons/tilearrow.svg"
import Link from 'next/link';

// Sample data - replace with your actual images and content
const momentsData = [
  {
    couple: "Andra & Jane",
    service: "Full-Service Planning",
    image: "/image/assests/moments/jane.png" 
  },
  {
    couple: "Mira & Jhon",
    service: "Full-Service Planning",
    image: "/image/assests/moments/jhon.png"
  },
  {
    couple: "<PERSON><PERSON> & Martin",
    service: "Partial Planning",
    image: "/image/assests/moments/martin.png"
  },
  {
    couple: "Miranda & Tom",
    service: "Full-Service Planning",
    image: "/image/assests/moments/jhon.png"
  }
];

const Moments = () => {
  return (
    <div className="py-16 px-4 md:px-8 max-w-7xl mx-auto">
      {/* Header section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-12">
        <div className="mb-6 md:mb-0 flex flex-col justify-between h-[7rem]">
         <div className='flex flex-row items-center gap-2'>
         <p className="text-sm uppercase tracking-wider text-gray-500 ">Our Work</p>
         <span className="w-36 h-[1px] bg-gray-300 "></span>
         </div>
          <p className="text-sm text-gray-600 max-w-xs">
            Take a look at some of the beautiful weddings we've had the pleasure of planning
          </p>
        </div>
        
        <h2 className="text-3xl md:text-4xl font-medium max-w-md ">
          Memorable Moments We've Created
        </h2>
      </div>
      

      {/* Carousel */}
      <Carousel
        opts={{
          align: "start",
          loop: true,
        }}
        className="w-full"
      >
          {/* Navigation buttons */}
          <div className="flex justify-end gap-2 mt-6 pb-4">
          <CarouselPrevious className="static bg-white border border-[#fc873f]  text-[#fc873f] hover:bg-gray-50 transform-none translate-y-0 translate-x-0 rounded-full" />
          <CarouselNext className="static bg-[#FE904B] hover:bg-[#fc873f] text-white transform-none translate-y-0 translate-x-0 rounded-full" />
        </div>
        <CarouselContent className="-ml-4">
          {momentsData.map((item, index) => (
            <CarouselItem key={index} className="pl-4 md:basis-1/2 lg:basis-1/3">
              <div className="bg-[#FEECE2] rounded-lg p-4 h-full">
                <div className="flex justify-between items-center mb-4 border-b border-[#13031F33] py-2">
                  <div className=''>
                    <h3 className="font-medium text-lg">{item.couple}</h3>
                    <p className="text-sm text-gray-600">{item.service}</p>
                  </div>
                <Link href="/gallary">
                <button className="bg-[#FE904B] rounded-full w-8 h-8 flex items-center justify-center">
                    <Image src={titlearrow} alt="Arrow" width={20} height={20} className="p-1"/>
                  </button>
                </Link>
                </div>
                <div className="relative h-[16rem] md:h-[20rem] w-full overflow-hidden rounded-md">
                  <Image 
                    src={item.image}
                    alt={item.couple}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        
      
      </Carousel>
    </div>
  );
};

export default Moments;