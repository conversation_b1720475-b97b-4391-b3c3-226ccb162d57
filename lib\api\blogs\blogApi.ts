// Blog API module
import { apiClient } from '../../customaxios';
import { buildUrl } from '../../globalurl';
import {
  Blog,
  BlogsResponse,
  BlogResponse,
  CreateBlogRequest,
  UpdateBlogRequest,
  BlogQueryParams,
} from './types';

// Blog API functions
export const blogApi = {
  // Get all blogs with pagination and filtering
  getBlogs: async (params: BlogQueryParams = {}): Promise<BlogsResponse> => {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params.category) queryParams.append('category', params.category);
    if (params.search) queryParams.append('search', params.search);
    if (params.keywords) queryParams.append('keywords', params.keywords);

    const url = `/blogs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiClient.get<BlogsResponse>(buildUrl(url));
    return response.data;
  },

  // Get single blog by ID
  getBlogById: async (id: string | number): Promise<BlogResponse> => {
    const response = await apiClient.get<BlogResponse>(buildUrl(`/blogs/${id}`));
    return response.data;
  },

  // Get blogs by category
  getBlogsByCategory: async (
    category: string,
    params: Omit<BlogQueryParams, 'category'> = {}
  ): Promise<BlogsResponse> => {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params.search) queryParams.append('search', params.search);
    if (params.keywords) queryParams.append('keywords', params.keywords);

    const url = `/blogs/category/${category}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiClient.get<BlogsResponse>(buildUrl(url));
    return response.data;
  },

  // Create new blog (Admin only)
  createBlog: async (blogData: CreateBlogRequest): Promise<BlogResponse> => {
    const formData = new FormData();

    formData.append('title', blogData.title);
    formData.append('description', blogData.description);

    // Handle category - convert array to string if needed
    if (Array.isArray(blogData.category)) {
      formData.append('category', blogData.category.join(','));
    } else {
      formData.append('category', blogData.category);
    }

    // Handle keywords - convert array to string if needed
    if (blogData.keywords) {
      if (Array.isArray(blogData.keywords)) {
        formData.append('keywords', blogData.keywords.join(','));
      } else {
        formData.append('keywords', blogData.keywords);
      }
    }

    // Handle image file
    if (blogData.image) {
      formData.append('image', blogData.image);
    }

    const response = await apiClient.post<BlogResponse>(
      buildUrl('/blogs'),
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  // Update blog (Admin only)
  updateBlog: async (blogData: UpdateBlogRequest): Promise<BlogResponse> => {
    const { id, ...updateData } = blogData;
    const formData = new FormData();

    if (updateData.title) formData.append('title', updateData.title);
    if (updateData.description) formData.append('description', updateData.description);

    if (updateData.category) {
      if (Array.isArray(updateData.category)) {
        formData.append('category', updateData.category.join(','));
      } else {
        formData.append('category', updateData.category);
      }
    }

    if (updateData.keywords) {
      if (Array.isArray(updateData.keywords)) {
        formData.append('keywords', updateData.keywords.join(','));
      } else {
        formData.append('keywords', updateData.keywords);
      }
    }

    if (updateData.image) {
      formData.append('image', updateData.image);
    }

    const response = await apiClient.put<BlogResponse>(
      buildUrl(`/blogs/${id}`),
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  // Delete blog (Admin only)
  deleteBlog: async (id: string | number): Promise<{ status: boolean; message: string }> => {
    const response = await apiClient.delete<{ status: boolean; message: string }>(
      buildUrl(`/blogs/${id}`)
    );
    return response.data;
  },

  // Search blogs
  searchBlogs: async (
    searchTerm: string,
    params: Omit<BlogQueryParams, 'search'> = {}
  ): Promise<BlogsResponse> => {
    return blogApi.getBlogs({ ...params, search: searchTerm });
  },

  // Get popular blogs (by views)
  getPopularBlogs: async (limit = 5): Promise<BlogsResponse> => {
    return blogApi.getBlogs({
      pageSize: limit,
      sortBy: 'views',
      sortOrder: 'desc',
    });
  },

  // Get recent blogs
  getRecentBlogs: async (limit = 5): Promise<BlogsResponse> => {
    return blogApi.getBlogs({
      pageSize: limit,
      sortBy: 'createdAt',
      sortOrder: 'desc',
    });
  },
};

// Helper functions for the existing blog components
export const getAllBlogs = async (params: {
  page?: number;
  limit?: number;
  pageSize?: number;
  search?: string;
  category?: string;
  keywords?: string;
}): Promise<BlogsResponse> => {
  return blogApi.getBlogs({
    page: params.page,
    pageSize: params.pageSize || params.limit,
    search: params.search,
    category: params.category,
    keywords: params.keywords,
  });
};

export const getBlogCategories = async (): Promise<{
  status: boolean;
  data: Array<{ name: string; count: number }>;
}> => {
  try {
    // Skip the categories endpoint and directly get from blogs
    // since your backend doesn't have /blogs/categories endpoint

    const blogsResponse = await blogApi.getBlogs({ pageSize: 1000 });

    if (!blogsResponse.status || !blogsResponse.data || !blogsResponse.data.data) {
      return {
        status: true,
        data: []
      };
    }

    const categories: { [key: string]: number } = {};

    blogsResponse.data.data.forEach(blog => {
      if (blog.category && Array.isArray(blog.category)) {
        blog.category.forEach(cat => {
          if (cat && typeof cat === 'string') {
            categories[cat] = (categories[cat] || 0) + 1;
          }
        });
      }
    });

    return {
      status: true,
      data: Object.entries(categories).map(([name, count]) => ({ name, count }))
    };
  } catch (error) {
    console.error('Error getting categories from blogs:', error);
    return {
      status: true,
      data: []
    };
  }
};

export const getRecentBlogs = async (limit = 5): Promise<BlogsResponse> => {
  return blogApi.getRecentBlogs(limit);
};

export const getBlogById = async (id: string | number): Promise<BlogResponse> => {
  return blogApi.getBlogById(id);
};

// Utility functions
export const formatBlogDate = (dateString: string): string => {
  try {
    if (!dateString) {
      return 'No Date';
    }

    // Check if the date is already in dd/mm/yyyy format from your backend
    if (dateString.includes('/') && dateString.includes(',')) {
      // Extract just the date part (before the comma)
      const datePart = dateString.split(',')[0];
      return datePart;
    }

    // If it's an ISO string, parse and format it
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid Date';
    }

    // Format as dd/mm/yyyy
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};



export const formatRelativeDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  } catch (error) {
    return 'Unknown';
  }
};

export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

export const createSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
};

export const getSocialShareUrls = (blogId: string, title: string, url: string) => {
  const encodedTitle = encodeURIComponent(title);
  const encodedUrl = encodeURIComponent(url);

  return {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    twitter: `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    whatsapp: `https://wa.me/?text=${encodedTitle} - ${encodedUrl}`,
    telegram: `https://t.me/share/url?url=${encodedUrl}&text=${encodedTitle}`,
    reddit: `https://reddit.com/submit?url=${encodedUrl}&title=${encodedTitle}`,
    pinterest: `https://pinterest.com/pin/create/button/?url=${encodedUrl}&description=${encodedTitle}`,
  };
};

// Export types for components
export type { Blog as BlogData } from './types';
