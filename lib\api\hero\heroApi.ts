// Hero Section API module
import { apiClient } from '../../customaxios';
import { buildUrl } from '../../globalurl';

// Types for hero API
export interface HeroSection {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  backgroundImage: string;
  ctaText: string;
  ctaLink: string;
  isActive: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface HeroSectionQueryParams {
  page?: number;
  limit?: number;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface HeroSectionsResponse {
  heroSections: HeroSection[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface HeroSectionResponse {
  success: boolean;
  message: string;
  data?: HeroSection;
}

// Hero API functions
export const heroApi = {
  // Get all hero sections with pagination and filters
  getHeroSections: async (params: HeroSectionQueryParams = {}): Promise<HeroSectionsResponse> => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get<HeroSectionsResponse>(
      buildUrl(`/hero-section?${queryParams.toString()}`)
    );
    return response.data;
  },

  // Get active hero sections (for public display)
  getActiveHeroSections: async (): Promise<HeroSectionsResponse> => {
    const response = await apiClient.get<HeroSectionsResponse>(
      buildUrl('/hero-section?isActive=true&sortBy=order&sortOrder=asc')
    );
    return response.data;
  },

  // Get single hero section by ID
  getHeroSectionById: async (id: string): Promise<HeroSectionResponse> => {
    const response = await apiClient.get<HeroSectionResponse>(
      buildUrl(`/hero-section/${id}`)
    );
    return response.data;
  },
};

// Helper functions
export const heroHelpers = {
  // Format hero section for display
  formatHeroSection: (heroSection: HeroSection) => {
    return {
      ...heroSection,
      formattedTitle: heroSection.title.toUpperCase(),
      shortDescription: heroSection.description.length > 150 
        ? heroSection.description.substring(0, 150) + '...'
        : heroSection.description,
    };
  },

  // Get hero section background style
  getBackgroundStyle: (imageUrl: string) => {
    return {
      backgroundImage: `url(${imageUrl})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
    };
  },

  // Validate hero section data
  validateHeroSection: (data: Partial<HeroSection>): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!data.title?.trim()) errors.push('Title is required');
    if (!data.subtitle?.trim()) errors.push('Subtitle is required');
    if (!data.description?.trim()) errors.push('Description is required');
    if (!data.backgroundImage?.trim()) errors.push('Background image is required');
    if (!data.ctaText?.trim()) errors.push('CTA text is required');
    if (!data.ctaLink?.trim()) errors.push('CTA link is required');

    // Validate URL format for CTA link
    if (data.ctaLink && !isValidUrl(data.ctaLink)) {
      errors.push('CTA link must be a valid URL');
    }

    // Validate image URL format
    if (data.backgroundImage && !isValidImageUrl(data.backgroundImage)) {
      errors.push('Background image must be a valid image URL');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
};

// Utility functions
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

const isValidImageUrl = (url: string): boolean => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
  const lowerUrl = url.toLowerCase();
  return imageExtensions.some(ext => lowerUrl.includes(ext)) || url.startsWith('data:image/');
};

// Export individual functions for easier imports
export const getHeroSections = heroApi.getHeroSections;
export const getActiveHeroSections = heroApi.getActiveHeroSections;
export const getHeroSectionById = heroApi.getHeroSectionById;
export const formatHeroSection = heroHelpers.formatHeroSection;
export const getBackgroundStyle = heroHelpers.getBackgroundStyle;
export const validateHeroSection = heroHelpers.validateHeroSection;
