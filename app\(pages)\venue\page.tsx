import React from "react";
import PageHero from "@/app/_components/page_hero/page_hero";
import Image from "next/image";
import Cards from "./components/cards";

const VenuePage = () => {
  const breadcrumbs = [
    { label: "HOME", href: "/" },
    { label: "VENUE", href: "/venue" }
  ];

  const venues = [
    {
      name: "Lakeside Manor",
      location: "Mumbai, India",
      capacity: "Up to 300 guests",
      description: "A stunning waterfront venue with panoramic views, perfect for romantic ceremonies and receptions.",
      image: "/image/assests/venue1.png"
    },
    {
      name: "Royal Gardens",
      location: "Delhi, India",
      capacity: "Up to 500 guests",
      description: "Elegant gardens surrounded by historic architecture, offering a majestic setting for your special day.",
      image: "/image/assests/venue2.png"
    },
    {
      name: "Beachfront Resort",
      location: "Goa, India",
      capacity: "Up to 200 guests",
      description: "Say your vows with your toes in the sand and the sound of waves in the background.",
      image: "/image/assests/venue3.png"
    },
    {
      name: "Mountain Retreat",
      location: "Shimla, India",
      capacity: "Up to 150 guests",
      description: "A serene mountain setting with breathtaking views, perfect for intimate celebrations.",
      image: "/image/assests/venue4.png"
    }
  ];

  return (
    <>
      <PageHero title="VENUE" breadcrumbs={breadcrumbs} />
      <Cards/>
      
    </>
  );
};

export default VenuePage;