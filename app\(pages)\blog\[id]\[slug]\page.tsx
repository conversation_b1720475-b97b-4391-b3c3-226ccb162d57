"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { BsArrowLeft } from "react-icons/bs";
import { FiShare2 } from "react-icons/fi";
import {
  getBlogById,
  getBlogCategories,
  getRecentBlogs,
  formatBlogDate,
  BlogData,
} from "@/lib/api/blogs/blogApi";
import BlogSidebar from "@/app/(pages)/blog/componets/BlogSidebar";
import BlogComments from "@/app/(pages)/blog/componets/BlogComments";
import ShareModal from "@/app/(pages)/blog/componets/ShareModal";

export default function BlogDetailPage() {
  const params = useParams();
  const router = useRouter();

  // Blog data state
  const [blog, setBlog] = useState<BlogData | null>(null);

  // Sidebar/filter related state
  const [categories, setCategories] = useState<Array<{ name: string; count: number }>>([]);
  const [recentBlogs, setRecentBlogs] = useState<BlogData[]>([]);

  // UI states
  const [loading, setLoading] = useState(true);
  const [showAllKeywords, setShowAllKeywords] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Initial data fetch
  useEffect(() => {
    if (params.id) {
      fetchBlogDetail(params.id as string);
      fetchCategories();
      fetchRecentBlogs();
    }
  }, [params.id]);

  // Fetch blog detail
  const fetchBlogDetail = async (id: string) => {
    try {
      setLoading(true);
      const response = await getBlogById(id);
      if (response.status) {
        setBlog(response.data);
      }
    } catch (error) {
      console.error("Error fetching blog detail:", error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await getBlogCategories();
      if (response.status) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  // Fetch recent blogs
  const fetchRecentBlogs = async () => {
    try {
      const response = await getRecentBlogs(3);
      if (response.status) {
        setRecentBlogs(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching recent blogs:", error);
    }
  };

  // Search blog
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/blog?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  // Search input change handler
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  // Filter blog by category
  const handleCategoryFilter = (category: string) => {
    router.push(`/blog?category=${encodeURIComponent(category)}`);
  };

  // Filter blog by keyword
  const handleKeywordClick = (keyword: string) => {
    router.push(`/blog?search=${encodeURIComponent(keyword)}`);
  };

  // Navigate to another blog post
  const handleBlogClick = (blogId: string, title: string) => {
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
    router.push(`/blog/${blogId}/${slug}`);
  };

  // Show loader while blog is loading
  if (loading) {
    return (
      <div className="min-h-screen bg-white text-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-900 text-xl">Loading blog...</div>
        </div>
      </div>
    );
  }

  // Show error if blog not found
  if (!blog) {
    return (
      <div className="min-h-screen bg-white text-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-900 text-xl mb-4">Blog not found</div>
          <Link href="/blog" className="text-orange-500 hover:text-orange-400">
            Back to Blogs
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white text-gray-900 py-10 md:py-16 lg:py-20 overflow-x-hidden">
      {/* Container */}
      <div className="w-[95%] max-w-[1400px] mx-auto flex flex-col lg:flex-row gap-6 lg:gap-8">
        {/* Mobile Sidebar Toggle */}
        <div className="lg:hidden flex justify-between items-center mb-4">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-gray-600 hover:text-orange-500 transition-colors"
          >
            <BsArrowLeft className="w-5 h-5" />
            <span className="text-sm">Back</span>
          </button>
          <button
            onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg text-sm"
          >
            {isMobileSidebarOpen ? "Hide Sidebar" : "Show Sidebar"}
          </button>
        </div>

        {/* Mobile Sidebar */}
        {isMobileSidebarOpen && (
          <div className="lg:hidden w-full mb-8">
            <BlogSidebar
              categories={categories}
              recentBlogs={recentBlogs}
              allKeywords={blog?.keywords || []}
              searchQuery={searchQuery}
              selectedCategory=""
              onSearchChange={handleSearchChange}
              onSearchSubmit={handleSearch}
              onCategoryFilter={handleCategoryFilter}
              onKeywordClick={handleKeywordClick}
            />
          </div>
        )}

        {/* Blog Content */}
        <div className="flex-1">
          {/* Back Button for Desktop */}
          <button
            onClick={() => router.back()}
            className="hidden lg:flex items-center gap-2 text-gray-600 hover:text-orange-500 mb-6 transition-colors"
          >
            <BsArrowLeft className="w-5 h-5" />
            <span>Back to Blogs</span>
          </button>

          {/* Header */}
          <div className="mb-6 md:mb-8">
            {/* Categories */}
            <div className="flex flex-wrap gap-2 mb-3 md:mb-4">
              {blog.category.map((cat, index) => (
                <span
                  key={index}
                  className="bg-orange-500 text-white px-2 py-1 md:px-3 rounded-full text-xs md:text-sm"
                >
                  {cat}
                </span>
              ))}
            </div>

            {/* Title */}
            <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight mb-3 md:mb-4">
              {blog.title}
            </h1>

            {/* Meta Info */}
            <div className="flex flex-wrap items-center gap-2 md:gap-4 text-gray-600 text-sm md:text-base mb-4 md:mb-6">
              <span>Published: {formatBlogDate(blog.createdAt)}</span>
              <span className="hidden md:inline">•</span>
              <span>Views: {blog.views}</span>
            </div>

            {/* Featured Image - Prevent Overflow */}
            <div className="relative w-full overflow-hidden h-[250px] sm:h-[350px] md:h-[450px] lg:h-[650px] rounded-[16px] md:rounded-[20px] mb-6 md:mb-8">
              {blog.imageUrl ? (
                <Image
                  src={blog.imageUrl}
                  alt={blog.title}
                  fill
                  className="object-cover w-full h-full"
                  priority
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-orange-500 to-orange-700 flex items-center justify-center">
                  <div className="text-white text-center p-8">
                    <div className="text-6xl md:text-8xl font-bold mb-4">📝</div>
                    <div className="text-xl md:text-2xl font-medium">Blog Post</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Blog HTML Content — with overflow hidden and break-words */}
          <div className="prose prose-gray max-w-none overflow-hidden break-words mb-6 md:mb-8">
            <div
              className="text-gray-800 text-base md:text-lg leading-relaxed"
              dangerouslySetInnerHTML={{ __html: blog.description }}
            />
          </div>

          {/* Keywords */}
          <div className="bg-gray-50 border border-gray-200 rounded-[16px] md:rounded-[20px] p-4 md:p-6 mb-6 md:mb-8">
            <h3 className="text-gray-900 text-lg md:text-xl font-bold mb-3 md:mb-4">Keywords</h3>
            <div className="flex flex-wrap gap-2">
              {blog.keywords
                .slice(0, showAllKeywords ? undefined : 5)
                .map((keyword, index) => (
                  <span
                    key={index}
                    className="bg-gray-200 text-gray-700 px-2 py-1 md:px-3 rounded-full text-xs md:text-sm"
                  >
                    {keyword.replace(/[\[\]"]/g, "")}
                  </span>
                ))}
              {blog.keywords.length > 5 && (
                <button
                  onClick={() => setShowAllKeywords(!showAllKeywords)}
                  className="text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium transition-colors"
                >
                  {showAllKeywords ? "Show Less" : "More"}
                </button>
              )}
            </div>
          </div>

          {/* Social Share Section */}
          <div className="bg-gray-50 border border-gray-200 rounded-[16px] md:rounded-[20px] p-4 md:p-6 mb-8">
            <h3 className="text-gray-900 text-lg md:text-xl font-bold mb-3 md:mb-4">
              Share this article
            </h3>
            <button
              onClick={() => setIsShareModalOpen(true)}
              className="flex items-center gap-2 bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 md:px-6 md:py-3 rounded-lg transition-colors text-sm md:text-base"
            >
              <FiShare2 className="w-4 h-4 md:w-5 md:h-5" />
              <span>Share Article</span>
            </button>
          </div>

          {/* Blog Comments */}
          <BlogComments
            blogId={Array.isArray(params.id) ? params.id[0] : params.id || undefined}
            blogTitle={blog?.title}
          />
        </div>

        {/* Sidebar - Desktop only */}
        <div className="hidden lg:block lg:w-[350px]">
          <BlogSidebar
            categories={categories}
            recentBlogs={recentBlogs}
            allKeywords={blog?.keywords || []}
            searchQuery={searchQuery}
            selectedCategory=""
            onSearchChange={handleSearchChange}
            onSearchSubmit={handleSearch}
            onCategoryFilter={handleCategoryFilter}
            onKeywordClick={handleKeywordClick}
          />
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        blogId={blog?._id || ""}
        blogTitle={blog?.title || ""}
        blogUrl={typeof window !== "undefined" ? window.location.href : ""}
      />
    </div>
  );
}
