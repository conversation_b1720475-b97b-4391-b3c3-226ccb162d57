// Blog and Comment Type Definitions

export interface Blog {
  _id: string;
  id: number;
  title: string;
  description: string;
  imageUrl?: string;
  category: string[];
  keywords: string[];
  views: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBlogRequest {
  title: string;
  description: string;
  category: string | string[];
  keywords?: string | string[];
  image?: File;
}

export interface UpdateBlogRequest extends Partial<CreateBlogRequest> {
  id: string;
}

export interface BlogsResponse {
  status: boolean;
  code: number;
  message: string;
  data: {
    data: Blog[];
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    currentPage: number;
    pageSize: number;
    filters?: {
      category?: string;
      search?: string;
      keywords?: string;
    };
  };
}

export interface BlogResponse {
  status: boolean;
  code: number;
  message: string;
  data: Blog;
}

export interface BlogQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  category?: string;
  search?: string;
  keywords?: string;
}

// Comment Types
export interface Comment {
  _id: string;
  id: number;
  blogId: string | number;
  name: string;
  email: string;
  comment: string;
  status: 'pending' | 'approved' | 'rejected' | 'spam';
  isVisible: boolean;
  moderatedBy?: string;
  moderatedAt?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCommentRequest {
  blogId: string | number;
  name: string;
  email: string;
  comment: string;
}

export interface UpdateCommentRequest {
  status?: 'pending' | 'approved' | 'rejected' | 'spam';
  isVisible?: boolean;
}

export interface CommentResponse {
  status: boolean;
  code: number;
  message: string;
  data: Comment | Comment[] | null;
}

export interface CommentsResponse {
  status: boolean;
  code: number;
  message: string;
  data: Comment[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

export interface CommentCountResponse {
  status: boolean;
  code: number;
  message: string;
  data: {
    blogId: string | number;
    commentCount: number;
  };
}

export interface CommentQueryParams {
  page?: number;
  limit?: number;
  status?: 'pending' | 'approved' | 'rejected' | 'spam';
  isVisible?: boolean;
  blogId?: string | number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CommentStatistics {
  status: boolean;
  code: number;
  message: string;
  data: {
    totalComments: number;
    pendingComments: number;
    approvedComments: number;
    rejectedComments: number;
    spamComments: number;
    recentComments: Comment[];
  };
}
