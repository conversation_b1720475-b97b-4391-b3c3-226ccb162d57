# Venue Booking System

## Overview
Complete venue listing and booking system with comprehensive filtering, search, and booking functionality. The system supports both API integration and demo data for development.

## Features Implemented

### ✅ Venue Listing
- **Comprehensive venue cards** showing name, location, capacity, seats, and venue type
- **Advanced filtering** by venue type (banquet-hall, outdoor, resort, hotel, farmhouse, palace, garden, beach, other)
- **Location-based filtering** by major cities
- **Search functionality** across venue names and locations
- **Sorting options** by price, capacity, and name
- **Pagination** with 9 venues per page
- **Responsive design** for all screen sizes

### ✅ Venue Types (Backend Compatible)
- Banquet Hall
- Outdoor
- Resort
- Hotel
- Farm House
- Palace
- Garden
- Beach
- Other

### ✅ Comprehensive Booking Form
- **Personal Information**: Name, email, phone, guest count
- **Address Details**: Street, city, state, pincode (Indian format)
- **Event Details**: Start date, end date (optional), event type
- **Budget Information**: Min/max budget with currency selection (INR/USD/EUR)
- **Requirements**: Catering, decoration, photography, music, parking
- **Special Requests**: Free text field for additional requirements
- **Form Validation**: Client-side validation with error messages
- **Success/Error Handling**: Toast notifications for user feedback

### ✅ API Integration
- **Venue API**: Fetch venues with filtering and pagination
- **Booking API**: Submit booking requests with full validation
- **Error Handling**: Graceful fallback to demo data on API errors
- **Loading States**: Spinner and loading indicators
- **Toast Notifications**: Success and error messages using react-toastify

### ✅ Data Toggle
- **API/Demo Toggle**: Switch between live API data and demo data
- **Seamless Fallback**: Automatic fallback to demo data if API fails
- **Development Mode**: Easy testing with toggle button

## File Structure

```
app/(pages)/venue/
├── components/
│   ├── cards.tsx              # Main venue listing component
│   ├── VenueCard.tsx          # Individual venue card
│   ├── booking-modal.tsx      # Comprehensive booking form modal
│   ├── filter.tsx             # Advanced filtering component
│   └── SortingDropdown.tsx    # Sorting options
├── venualldummydata/
│   └── venue_dummydata.tsx    # Demo data (updated to match backend schema)
├── page.tsx                   # Main venue page
└── README.md                  # This file

api/venue/
├── types.ts                   # TypeScript interfaces for venue system
├── venueApi.ts               # API functions and helpers
├── validation.ts             # Form validation logic
└── index.ts                  # Module exports
```

## Usage

### Basic Usage
The venue system is automatically integrated into the `/venue` page. Users can:

1. **Browse venues** with filtering and search
2. **Click "Book Now"** to open the booking modal
3. **Fill the comprehensive form** with all required details
4. **Submit booking** and receive confirmation

### API Integration
```typescript
// Toggle between API and demo data
const [useApiData, setUseApiData] = useState(false);

// Fetch venues from API
const venues = await venueApi.getVenues({
  page: 1,
  limit: 10,
  venueType: 'banquet-hall',
  search: 'mumbai'
});

// Submit booking
const booking = await venueApi.submitBooking(formData);
```

### Form Validation
```typescript
import { validateVenueBooking } from '@/api/venue';

const validation = validateVenueBooking(formData);
if (!validation.isValid) {
  // Handle validation errors
  console.log(validation.errors);
}
```

## Backend Integration

### Required Environment Variables
```env
BASE_URL=http://localhost:8005/api
```

### API Endpoints Used
- `GET /venues` - Fetch venues with filtering
- `POST /venue-bookings` - Submit booking requests

### Data Schema
The system follows the backend schema exactly:
- **Venue Model**: _id, name, image (S3 URL), venueType, location, capacity, seats
- **Booking Model**: Complete booking form with address, event details, requirements

## Toast Notifications

### Success Messages
- **Booking Success**: "🎉 Thank you for your booking request! We'll get back to you soon."

### Error Messages
- **Validation Errors**: "Please fix the form errors before submitting."
- **API Errors**: Specific error messages from backend
- **Network Errors**: "Failed to submit booking request. Please try again."

## Development Features

### Toggle Button
- Switch between API and demo data for testing
- Visual indicator of current data source
- Automatic fallback on API errors

### Loading States
- Spinner during API calls
- Skeleton loading for better UX
- Error states with retry functionality

### Responsive Design
- Mobile-first approach
- Optimized for all screen sizes
- Touch-friendly interface

## Future Enhancements

### Potential Improvements
1. **Image Gallery**: Multiple images per venue
2. **Venue Details Page**: Dedicated page for each venue
3. **Booking Management**: Admin panel for managing bookings
4. **Payment Integration**: Online payment processing
5. **Calendar Integration**: Availability checking
6. **Reviews & Ratings**: User feedback system

### Performance Optimizations
1. **Image Optimization**: Next.js Image component with proper sizing
2. **Lazy Loading**: Implement virtual scrolling for large datasets
3. **Caching**: API response caching with React Query
4. **Search Optimization**: Debounced search with backend integration

## Testing

### Manual Testing Checklist
- [ ] Venue listing loads correctly
- [ ] Filtering works for all venue types
- [ ] Location filtering functions properly
- [ ] Search finds relevant venues
- [ ] Sorting options work correctly
- [ ] Pagination navigates properly
- [ ] Booking modal opens and closes
- [ ] Form validation catches errors
- [ ] Successful booking shows toast
- [ ] API toggle switches data sources
- [ ] Error states display correctly
- [ ] Mobile responsiveness works

### API Testing
- [ ] Venue API returns proper data
- [ ] Booking API accepts submissions
- [ ] Error handling works correctly
- [ ] Fallback to demo data functions
- [ ] Toast notifications appear

This venue system provides a complete, production-ready solution for venue booking with excellent user experience and robust error handling.
