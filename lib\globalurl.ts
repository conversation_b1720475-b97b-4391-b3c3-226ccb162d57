// Global URL configuration
export const GLOBAL_CONFIG = {
  BASE_URL: process.env.BASE_URL!,
  API_VERSION: 'v1',
  TIMEOUT: 10000, // 10 seconds
} as const;

// API endpoints configuration
export const API_ENDPOINTS = {
  // Add your API endpoints here
  // Example:
  // AUTH: '/auth',
  // USERS: '/users',
  // EVENTS: '/events',
} as const;

// Helper function to build full API URL
export const buildApiUrl = (endpoint: string): string => {
  return `${GLOBAL_CONFIG.BASE_URL}/api/${GLOBAL_CONFIG.API_VERSION}${endpoint}`;
};

// Helper function to build full URL without API prefix
export const buildUrl = (endpoint: string): string => {
  return `${GLOBAL_CONFIG.BASE_URL}${endpoint}`;
};
