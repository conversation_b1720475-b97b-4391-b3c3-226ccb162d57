"use client";
import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import middleicon from "@/public/image/icons/about/middleicons.svg";
import leaficons from "@/public/image/icons/about/leaficons.svg";
import goodrateicons from "@/public/image/icons/about/goodrateicons.svg";
import happyclientsicons from "@/public/image/icons/about/happyclientsicons.svg";

const TotalNumber = () => {
  const [yearsCount, setYearsCount] = useState(0);
  const [awardsCount, setAwardsCount] = useState(0);
  const [eventsCount, setEventsCount] = useState(0);
  const [clientsCount, setClientsCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    const currentRef = sectionRef.current; // Store ref in a variable
    
    const observer = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        animateNumbers();
        observer.disconnect();
      }
    }, { threshold: 0.1 });
    
    if (currentRef) {
      observer.observe(currentRef);
    }
    
    return () => {
      if (currentRef) { // Use the stored variable here
        observer.unobserve(currentRef);
      }
    };
  }, []);

  const animateNumbers = () => {
    const duration = 2000; // 2 seconds
    const frameDuration = 1000 / 60; // 60fps
    const totalFrames = Math.round(duration / frameDuration);
    
    let frame = 0;
    
    const targetValues = {
      years: 26,
      awards: 15,
      events: 800,
      clients: 1000
    };
    
    const counter = setInterval(() => {
      frame++;
      
      const progress = frame / totalFrames;
      
      setYearsCount(Math.floor(progress * targetValues.years));
      setAwardsCount(Math.floor(progress * targetValues.awards));
      setEventsCount(Math.floor(progress * targetValues.events));
      setClientsCount(Math.floor(progress * targetValues.clients));
      
      if (frame === totalFrames) {
        clearInterval(counter);
        setYearsCount(targetValues.years);
        setAwardsCount(targetValues.awards);
        setEventsCount(targetValues.events);
        setClientsCount(targetValues.clients);
      }
    }, frameDuration);
  };

  return (
    <div className="px-4 sm:px-6 md:px-8 relative z-30" style={{ marginBottom: "-80px" }} ref={sectionRef}>
      <div className="max-w-5xl mx-auto">
        <div className="bg-white rounded-lg shadow-md py-8 px-4 sm:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-4">
            {/* Years of Experience */}
            <div className="flex flex-row gap-x-4 items-center text-center">
              <div className="mb-3">
                <Image 
                  src={leaficons} 
                  alt="Years of Experience" 
                  width={40} 
                  height={40}
                />
              </div>
              
              <div className='flex flex-col items-start justify-start'>
              <h3 className="text-2xl sm:text-3xl font-bold">{yearsCount}+</h3>
              <p className="text-xs sm:text-sm text-gray-500">Years of Experience</p>
              </div>
            </div>
            
            {/* Awards */}
            <div className="flex flex-row gap-x-4 items-center text-center">
              <div className="mb-3">
                <Image 
                  src={middleicon} 
                  alt="Awards" 
                  width={40} 
                  height={40}
                />
              </div>
              <div className='flex flex-col items-start justify-start'>
              <h3 className="text-2xl sm:text-3xl font-bold">{awardsCount}+</h3>
              <p className="text-xs sm:text-sm text-gray-500">Awards</p>
              </div>
            </div>
            
            {/* Events Organized */}
            <div className="flex flex-row gap-x-4 items-center text-center">
              <div className="mb-3">
                <Image 
                  src={goodrateicons} 
                  alt="Events Organized" 
                  width={40} 
                  height={40}
                />
              </div>
              <div className='flex flex-col items-start justify-start'>
              <h3 className="text-2xl sm:text-3xl font-bold">{eventsCount}+</h3>
              <p className="text-xs sm:text-sm text-gray-500">Events Organized</p>
              </div>
            </div>
            
            {/* Happy Clients */}
            <div className="flex flex-row gap-x-4 items-center text-center">
              <div className="mb-3">
                <Image 
                  src={happyclientsicons} 
                  alt="Happy Clients" 
                  width={40} 
                  height={40}
                />
              </div>
              <div className='flex flex-col items-start justify-start'>
              <h3 className="text-2xl sm:text-3xl font-bold">{clientsCount}+</h3>
              <p className="text-xs sm:text-sm text-gray-500">Happy Clients</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TotalNumber;
