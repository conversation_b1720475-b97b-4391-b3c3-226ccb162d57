"use client";
import React from 'react';
import Image from 'next/image';
import rightflower from "@/public/image/assests/borderflower.png";
import { ArrowRight } from 'lucide-react';
import tiltarrow from "@/public/image/icons/tilearrow.svg"
import Link from 'next/link';

const GetTouch = () => {
  return (
    <div className="relative w-full overflow-hidden">
      {/* Background with gradient */}
      <div 
        className="w-full py-16 lg:py-24 bg-gradient-to-br from-[#FFEBDE] via-white to-[#FFEBDE]  "
        
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 relative z-10">
          <div className="flex flex-row items-center justify-center">
            {/* Text section - Centered */}
            <div className="w-full mb-8 text-center">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl text-left font-medium text-[#13031F] leading-tight max-w-md mx-auto">
                Get in touch with us to schedule a consultation
              </h2>
            </div>
            
            {/* Button section - Centered */}
         
           <div className="w-full flex flex-col sm:flex-row justify-center sm:justify-start items-center gap-2 sm:gap-0">
             <Link href={"/contact"} className="flex items-center">
               <button className="bg-[#FE904B] hover:bg-[#e87f3d] text-white px-4 sm:px-6 py-2 sm:py-3 rounded-full flex items-center gap-2 transition-all duration-300 shadow-lg text-sm sm:text-base">
                 Get Started
               </button>
               <span className="px-[5px] sm:px-[7px] bg-[#FE904B] rounded-full flex items-center justify-center ml-1">
                 <Image src={tiltarrow} alt="Arrow" width={30} height={30} className='p-1 sm:p-2' />
               </span>
             </Link>
           </div>
           
          </div>
        </div>
        
        {/* Flower decoration on right */}
        <div className="absolute top-0 right-0 h-full">
          <div className="relative h-full">
            <Image
              src={rightflower}
              alt="Decorative flower"
              className="h-full w-auto object-contain object-right"
              priority
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GetTouch;
