"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { BsSearch } from "react-icons/bs";
import { formatBlogDate, BlogData } from "@/lib/api/blogs/blogApi";

interface BlogSidebarProps {
  categories: Array<{ name: string; count: number }>;
  recentBlogs: BlogData[];
  allKeywords: string[];
  searchQuery: string;
  selectedCategory: string;
  onSearchChange: (query: string) => void;
  onSearchSubmit: (e: React.FormEvent) => void;
  onCategoryFilter: (category: string) => void;
  onKeywordClick: (keyword: string) => void;
}

export default function BlogSidebar({
  categories,
  recentBlogs,
  allKeywords,
  searchQuery,
  selectedCategory,
  onSearchChange,
  onSearchSubmit,
  onCategoryFilter,
  onKeywordClick,
}: BlogSidebarProps) {
  const router = useRouter();
  const [showAllCategories, setShowAllCategories] = useState(false);
  const [showAllKeywords, setShowAllKeywords] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleBlogClick = (blogId: string, title: string) => {
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
    router.push(`/blog/${blogId}/${slug}`);
  };

  return (
    <div className="w-full lg:w-[350px] flex flex-col gap-6 md:gap-8">
      {/* Search Section */}
      <div className="bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6">
        <h3 className="text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4">
          Search
        </h3>
        <form onSubmit={onSearchSubmit} className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder="Search blogs..."
            className="w-full bg-gray-50 border border-gray-300 rounded-lg px-4 py-2 md:py-3 pr-10 md:pr-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-orange-500 text-sm md:text-base"
          />
          <button
            type="submit"
            className="absolute right-2 md:right-3 top-1/2 transform -translate-y-1/2 text-gray-600 hover:text-orange-500 transition-colors"
          >
            <BsSearch className="w-4 h-4 md:w-5 md:h-5" />
          </button>
        </form>
      </div>

      {/* Categories Section */}
      <div className="bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6">
        <h3 className="text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4">
          Category
        </h3>
        <div className="space-y-2 md:space-y-3">
          {categories
            .slice(0, showAllCategories ? categories.length : isMobile ? 3 : 5)
            .map((category, index) => (
              <div
                key={index}
                onClick={() => onCategoryFilter(category.name)}
                className={`flex justify-between items-center p-2 md:p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedCategory === category.name
                    ? "bg-orange-500 text-white"
                    : "bg-gray-50 text-gray-700 hover:bg-gray-100"
                }`}
              >
                <span className="font-medium text-sm md:text-base">
                  {category.name}
                </span>
                <span className="text-xs md:text-sm">({category.count})</span>
              </div>
            ))}
          {categories.length > (isMobile ? 3 : 5) && (
            <button
              onClick={() => setShowAllCategories(!showAllCategories)}
              className="w-full text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium py-1 md:py-2 transition-colors"
            >
              {showAllCategories ? "Show Less" : "More"}
            </button>
          )}
        </div>
      </div>

      {/* Recent News Section */}
      <div className="bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6">
        <h3 className="text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4">
          Recent News
        </h3>
        <div className="space-y-3 md:space-y-4">
          {recentBlogs.map((blog) => (
            <div
              key={blog._id}
              onClick={() => handleBlogClick(blog._id, blog.title)}
              className="flex gap-2 md:gap-3 cursor-pointer group"
            >
              <div className="w-12 h-12 md:w-16 md:h-16 rounded-lg overflow-hidden flex-shrink-0">
                <Image
                  src={blog.imageUrl}
                  alt={blog.title}
                  width={64}
                  height={64}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
              </div>
              <div className="flex-1">
                <h4 className="text-gray-900 text-xs md:text-sm font-medium line-clamp-2 group-hover:text-orange-500 transition-colors">
                  {blog.title}
                </h4>
                <p className="text-gray-500 text-[10px] md:text-xs mt-0.5 md:mt-1">
                  {formatBlogDate(blog.createdAt)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Keywords Section */}
      <div className="bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6">
        <h3 className="text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4">
          Keywords
        </h3>
        <div className="flex flex-wrap gap-1 md:gap-2">
          {allKeywords
            .slice(0, showAllKeywords ? undefined : isMobile ? 5 : 10)
            .map((keyword, index) => (
              <span
                key={index}
                onClick={() => onKeywordClick(keyword)}
                className="bg-gray-100 hover:bg-orange-500 text-gray-700 hover:text-white px-2 py-0.5 md:px-3 md:py-1 rounded-full text-xs md:text-sm cursor-pointer transition-colors"
              >
                {keyword.replace(/[\[\]"]/g, "")}
              </span>
            ))}
          {allKeywords.length > (isMobile ? 5 : 10) && (
            <button
              onClick={() => setShowAllKeywords(!showAllKeywords)}
              className="text-orange-500 hover:text-orange-400 text-xs md:text-sm font-medium transition-colors"
            >
              {showAllKeywords ? "Less" : "More"}
            </button>
          )}
        </div>
      </div>

      {/* Any Questions Section */}
      <div className="bg-white border border-gray-200 shadow-lg rounded-[16px] md:rounded-[20px] p-4 md:p-6">
        <h3 className="text-gray-900 text-[18px] md:text-[20px] font-bold mb-3 md:mb-4">
          Any Questions?
        </h3>
        <p className="text-gray-600 text-xs md:text-sm mb-3 md:mb-4">
          Have questions about our services or need help with your project?
        </p>
        <Link
          href="/contact"
          className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-2 px-3 md:py-3 md:px-4 rounded-lg transition-colors text-center block text-sm md:text-base"
        >
          Let's Talk
        </Link>
      </div>
    </div>
  );
}
