'use client'
import React, { useState } from "react";
import PageHero from "@/app/_components/page_hero/page_hero";
import { Mail, Phone, MapPin, Clock } from "lucide-react";
import { contactApi, ContactFormData, validateContactForm } from "@/lib/api/contact/contactApi";
import { toast } from 'react-toastify';

const ContactPage = () => {
  const breadcrumbs = [
    { label: "HOME", href: "/" },
    { label: "CONTACT", href: "/contact" }
  ];

  // Form state
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    countryCode: '+91',
    phoneNumber: '',
    service: '',
    message: ''
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validation = validateContactForm(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const response = await contactApi.submitContact(formData);

      if (response.success) {
        // Show success toast
        toast.success('🎉 Thank you for your message! We\'ll get back to you soon.', {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });

        // Reset form
        setFormData({
          name: '',
          email: '',
          countryCode: '+91',
          phoneNumber: '',
          service: '',
          message: ''
        });
      } else {
        // Show error toast
        toast.error(response.message || 'Failed to send message. Please try again.', {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
    } catch (error: any) {
      console.error('Contact form error:', error);

      if (error.errors) {
        setErrors(error.errors);
        toast.error('Please fix the errors below and try again.', {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      } else {
        toast.error(error.message || 'Failed to send message. Please try again.', {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <PageHero title="CONTACT" breadcrumbs={breadcrumbs} />
      
      <div className="min-h-screen py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <h2 className="text-3xl font-medium mb-6">Get In Touch</h2>
              <p className="text-gray-700 mb-8">
                We'd love to hear from you! Whether you're ready to start planning your special day 
                or just have questions, our team is here to help.
              </p>
              
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="bg-[#FEF2EB] p-3 rounded-full mr-4">
                    <Phone className="h-5 w-5 text-[#FE904B]" />
                  </div>
                  <div>
                    <h3 className="font-medium">Phone</h3>
                    <p className="text-gray-700">+91-9735284928</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-[#FEF2EB] p-3 rounded-full mr-4">
                    <Mail className="h-5 w-5 text-[#FE904B]" />
                  </div>
                  <div>
                    <h3 className="font-medium">Email</h3>
                    <p className="text-gray-700"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-[#FEF2EB] p-3 rounded-full mr-4">
                    <MapPin className="h-5 w-5 text-[#FE904B]" />
                  </div>
                  <div>
                    <h3 className="font-medium">Address</h3>
                    <p className="text-gray-700">123 Wedding Lane, Mumbai, India 400001</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-[#FEF2EB] p-3 rounded-full mr-4">
                    <Clock className="h-5 w-5 text-[#FE904B]" />
                  </div>
                  <div>
                    <h3 className="font-medium">Hours</h3>
                    <p className="text-gray-700">Monday - Friday: 9am - 6pm</p>
                    <p className="text-gray-700">Saturday: 10am - 4pm</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Contact Form */}
            <div className="bg-[#FEF2EB] p-8 rounded-lg">
              <h2 className="text-2xl font-medium mb-6">Send Us a Message</h2>



              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Row 1: Name and Email */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                        errors.name ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Your full name"
                      required
                    />
                    {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                        errors.email ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="<EMAIL>"
                      required
                    />
                    {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                  </div>
                </div>

                {/* Row 2: Country Code and Phone Number */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <label htmlFor="countryCode" className="block text-sm font-medium text-gray-700 mb-1">
                      Country Code *
                    </label>
                    <select
                      id="countryCode"
                      name="countryCode"
                      value={formData.countryCode}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                        errors.countryCode ? 'border-red-500' : 'border-gray-300'
                      }`}
                      required
                    >
                      <option value="+91">+91 (India)</option>
                      <option value="+1">+1 (US/Canada)</option>
                      <option value="+44">+44 (UK)</option>
                      <option value="+61">+61 (Australia)</option>
                      <option value="+971">+971 (UAE)</option>
                      <option value="+65">+65 (Singapore)</option>
                    </select>
                    {errors.countryCode && <p className="text-red-500 text-xs mt-1">{errors.countryCode}</p>}
                  </div>

                  <div className="sm:col-span-2">
                    <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      id="phoneNumber"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                        errors.phoneNumber ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="9876543210"
                      required
                    />
                    {errors.phoneNumber && <p className="text-red-500 text-xs mt-1">{errors.phoneNumber}</p>}
                  </div>
                </div>

                {/* Row 3: Service */}
                <div>
                  <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-1">
                    Service Required *
                  </label>
                  <input
                    type="text"
                    id="service"
                    name="service"
                    value={formData.service}
                    onChange={handleChange}
                    className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                      errors.service ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="e.g., Wedding Planning, Event Management, Venue Booking"
                    required
                  />
                  {errors.service && <p className="text-red-500 text-xs mt-1">{errors.service}</p>}
                </div>

                {/* Row 4: Message */}
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={5}
                    className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FE904B] ${
                      errors.message ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Tell us about your requirements..."
                    required
                  ></textarea>
                  {errors.message && <p className="text-red-500 text-xs mt-1">{errors.message}</p>}
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.message.length}/1000 characters
                  </p>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full px-6 py-3 rounded-full transition-colors ${
                    isSubmitting
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-[#FE904B] hover:bg-[#e87f3d] text-white'
                  }`}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContactPage;
