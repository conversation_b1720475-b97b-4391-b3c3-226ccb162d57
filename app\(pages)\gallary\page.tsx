"use client";
import React, { useState, useEffect } from "react";
import <PERSON>H<PERSON> from "@/app/_components/page_hero/page_hero";
import Image from "next/image";
import { galleryApi, Gallery, GalleryCategory, serviceHelpers } from '@/lib/api/service/serviceApi';
import { toast } from 'react-toastify';
import { ChevronLeft, ChevronRight } from "lucide-react";
import Lightbox from "yet-another-react-lightbox";
import Zoom from "yet-another-react-lightbox/plugins/zoom";
import Thumbnails from "yet-another-react-lightbox/plugins/thumbnails";
import "yet-another-react-lightbox/styles.css";
import "yet-another-react-lightbox/plugins/thumbnails.css";

const GallaryPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [isOpen, setIsOpen] = useState(false);
  const [photoIndex, setPhotoIndex] = useState(0);
  const [galleryItems, setGalleryItems] = useState<Gallery[]>([]);
  const [filteredItems, setFilteredItems] = useState<Gallery[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<GalleryCategory | ''>('');
  const [availableCategories, setAvailableCategories] = useState<GalleryCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const imagesPerPage = 6;

  // Calculate total pages
  const totalPages = Math.ceil(filteredItems.length / imagesPerPage);

  // Get current page images
  const indexOfLastImage = currentPage * imagesPerPage;
  const indexOfFirstImage = indexOfLastImage - imagesPerPage;
  const currentImages = filteredItems.slice(indexOfFirstImage, indexOfLastImage);

  // Format images for lightbox
  const lightboxImages = filteredItems.map(item => ({
    src: item.image,
    alt: item.title
  }));

  const breadcrumbs = [
    { label: "HOME", href: "/" },
    { label: "GALLARY", href: "/gallary" }
  ];

  // Fetch gallery items from API
  const fetchGalleryItems = async (category?: GalleryCategory) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await galleryApi.getGalleryItems({
        page: 1,
        limit: 100, // Get all items for filtering
        category: category,
        sortBy: 'sortOrder',
        sortOrder: 'asc'
      });

      if (response.success && response.data.galleries) {
        setGalleryItems(response.data.galleries);
        setFilteredItems(response.data.galleries);

        // Extract unique categories from the data
        const categories = [...new Set(response.data.galleries.map(item => item.category))];
        setAvailableCategories(categories);
      } else {
        throw new Error('Failed to fetch gallery items');
      }
    } catch (err: any) {
      console.error('Error fetching gallery:', err);
      setError(err.message || 'Failed to load gallery');
      toast.error('Failed to load gallery. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter items by category
  const filterByCategory = (category: GalleryCategory | '') => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page

    if (category === '') {
      setFilteredItems(galleryItems);
    } else {
      const filtered = galleryItems.filter(item => item.category === category);
      setFilteredItems(filtered);
    }
  };

  useEffect(() => {
    fetchGalleryItems();
  }, []);
  
  // Handle page changes
  const goToPage = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };
  
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  // Open lightbox with specific image
  const openLightbox = (index: number) => {
    setPhotoIndex(indexOfFirstImage + index);
    setIsOpen(true);
  };

  return (
    <>
      <PageHero title="GALLARY" breadcrumbs={breadcrumbs} />
      
      <div className="py-20 px-4 sm:px-20 lg:px-10">
        <div className="max-w-5xl mx-auto">
          {/* Gallery Title and Tagline */}
          <div className="text-center mb-8">
            <p className="text-[12px] text-[#BC7B77] uppercase tracking-wider mb-2">PHOTO GALLERY</p>
            <h2 className="text-[32px] sm:text-4xl font-medium mb-4 uppercase">
              Our Professional Gallery
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto mb-8">
              Explore our collection of beautiful events and celebrations we've created for our clients.
            </p>
          </div>

          {/* Category Filters */}
          {!isLoading && availableCategories.length > 0 && (
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              <button
                onClick={() => filterByCategory('')}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === ''
                    ? 'bg-[#FE904B] text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                All
              </button>
              {availableCategories.map((category) => (
                <button
                  key={category}
                  onClick={() => filterByCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-[#FE904B] text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {serviceHelpers.formatGalleryCategory(category)}
                </button>
              ))}
            </div>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="grid grid-cols-12 gap-4">
              {[...Array(6)].map((_, index) => (
                <div key={index} className={`${index < 3 ? 'col-span-12 sm:col-span-4' : index === 3 ? 'col-span-12 sm:col-span-8' : index === 4 ? 'col-span-12 sm:col-span-4' : 'col-span-12'}`}>
                  <div className={`bg-gray-300 animate-pulse rounded-md ${index < 3 || index === 4 ? 'aspect-square' : index === 3 ? 'aspect-[16/9]' : 'aspect-[21/9]'}`}></div>
                </div>
              ))}
            </div>
          )}

          {/* Error State */}
          {error && !isLoading && (
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-lg font-medium">Failed to load gallery</p>
                <p className="text-sm text-gray-600 mt-2">{error}</p>
              </div>
              <button
                onClick={() => fetchGalleryItems()}
                className="px-4 py-2 bg-[#FE904B] text-white rounded-md hover:bg-[#e87f3a] transition-colors"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !error && filteredItems.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-lg font-medium">No images found</p>
                <p className="text-sm text-gray-600 mt-2">
                  {selectedCategory ? `No images in ${serviceHelpers.formatGalleryCategory(selectedCategory)} category` : 'No gallery images available'}
                </p>
              </div>
            </div>
          )}

          {/* Gallery Grid */}
          {!isLoading && !error && currentImages.length > 0 && (
            <div className="grid grid-cols-12 gap-4">
              {/* First row - 3 images */}
              {currentImages.length > 0 && (
                <div className="col-span-12 sm:col-span-4">
                  <div
                    className="relative aspect-square overflow-hidden rounded-md cursor-pointer group"
                    onClick={() => openLightbox(0)}
                  >
                    <Image
                      src={currentImages[0].image}
                      alt={currentImages[0].title}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {currentImages.length > 1 && (
                <div className="col-span-12 sm:col-span-4">
                  <div
                    className="relative aspect-square overflow-hidden rounded-md cursor-pointer group"
                    onClick={() => openLightbox(1)}
                  >
                    <Image
                      src={currentImages[1].image}
                      alt={currentImages[1].title}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {currentImages.length > 2 && (
                <div className="col-span-12 sm:col-span-4">
                  <div
                    className="relative aspect-square overflow-hidden rounded-md cursor-pointer group"
                    onClick={() => openLightbox(2)}
                  >
                    <Image
                      src={currentImages[2].image}
                      alt={currentImages[2].title}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Second row - 2 images (wide + normal) */}
              {currentImages.length > 3 && (
                <div className="col-span-12 sm:col-span-8">
                  <div
                    className="relative aspect-[16/9] overflow-hidden rounded-md cursor-pointer group"
                    onClick={() => openLightbox(3)}
                  >
                    <Image
                      src={currentImages[3].image}
                      alt={currentImages[3].title}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {currentImages.length > 4 && (
                <div className="col-span-12 sm:col-span-4">
                  <div
                    className="relative aspect-square overflow-hidden rounded-md cursor-pointer group"
                    onClick={() => openLightbox(4)}
                  >
                    <Image
                      src={currentImages[4].image}
                      alt={currentImages[4].title}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Third row - 1 image (wide) */}
              {currentImages.length > 5 && (
                <div className="col-span-12">
                  <div
                    className="relative aspect-[21/9] overflow-hidden rounded-md cursor-pointer group"
                    onClick={() => openLightbox(5)}
                  >
                    <Image
                      src={currentImages[5].image}
                      alt={currentImages[5].title}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Pagination */}
          {!isLoading && !error && filteredItems.length > imagesPerPage && (
            <div className="flex justify-center items-center mt-12 space-x-2">
            <button 
              onClick={goToPreviousPage}
              disabled={currentPage === 1}
              className={`w-8 h-8 flex items-center justify-center rounded-md border ${
                currentPage === 1 ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 hover:bg-gray-100 cursor-pointer'
              }`}
            >
              <ChevronLeft size={16} />
            </button>
            
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button 
                key={page}
                onClick={() => goToPage(page)}
                className={`w-8 h-8 flex items-center justify-center rounded-md ${
                  currentPage === page 
                    ? 'bg-[#FE904B] text-white' 
                    : 'border border-gray-300 hover:bg-gray-100'
                }`}
              >
                {page}
              </button>
            ))}
            
              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className={`w-8 h-8 flex items-center justify-center rounded-md border ${
                  currentPage === totalPages ? 'border-gray-200 text-gray-400 cursor-not-allowed' : 'border-gray-300 hover:bg-gray-100 cursor-pointer'
                }`}
              >
                <ChevronRight size={16} />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Lightbox */}
      <Lightbox
        open={isOpen}
        close={() => setIsOpen(false)}
        slides={lightboxImages}
        index={photoIndex}
        plugins={[Zoom, Thumbnails]}
        zoom={{
          maxZoomPixelRatio: 3,
          zoomInMultiplier: 1.5,
          doubleTapDelay: 300,
        }}
        thumbnails={{
          position: "bottom",
          width: 120,
          height: 80,
          gap: 16,
        }}
      />
    </>
  );
};

export default GallaryPage;
