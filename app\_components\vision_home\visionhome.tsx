"use client";
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import leafflower from "@/public/image/assests/bigflower.png";
import { ArrowLeft, ArrowRight } from 'lucide-react';

// Sample data for the steps
const stepsData = [
  {
    id: "01",
    title: "Login",
    description: "Receive your personalized wedding planning dashboard! Stay organized on every detail of the details for your big day.",
    image: "/image/assests/event1.png" // Replace with actual image path
  },
  {
    id: "02",
    title: "Consultation",
    description: "Meet with our expert planners to discuss your vision, preferences, and budget for your special day.",
    image: "/image/assests/event2.png" // Replace with actual image path
  },
  {
    id: "03",
    title: "Planning",
    description: "We create a detailed timeline and coordinate with vendors to ensure everything runs smoothly.",
    image: "/image/assests/event3.png" // Replace with actual image path
  },
  {
    id: "04",
    title: "Execution",
    description: "On your big day, we handle all the details so you can focus on creating memories that last a lifetime.",
    image: "/image/assests/event1.png" // Replace with actual image path
  }
];

const VisionHome = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [progress, setProgress] = useState(25); // Initial progress percentage
  const currentStep = stepsData[activeStep];
  
  // Update progress when active step changes
  useEffect(() => {
    const newProgress = ((activeStep + 1) / stepsData.length) * 100;
    setProgress(newProgress);
  }, [activeStep]);
  
  const handlePrevStep = () => {
    setActiveStep((prev) => (prev === 0 ? stepsData.length - 1 : prev - 1));
  };
  
  const handleNextStep = () => {
    setActiveStep((prev) => (prev === stepsData.length - 1 ? 0 : prev + 1));
  };

  return (
    <div className="py-16 px-0 md:px-8 w-full bg-[#F8E7DD]">
      <div className="flex flex-col lg:flex-row gap-8 lg:gap-16 items-center justify-between">
        <div>
            {/* Left Section */}
        <div className="lg:w-1/2 h- relative px-4">
          <div className='flex flex-row items-center gap-2 w-full'>
          <p className="text-sm uppercase tracking-wider text-gray-500 ">Our Process</p>
          <span className="w-[4rem] h-px bg-gray-300 "></span>
          </div>
          
          <h2 className="text-4xl md:text-6xl font-medium mb-8 md:w-[33rem] w-full">
            How We Bring Your Vision to Life
          </h2>
          
          {/* Navigation buttons */}
          <div className="flex gap-2 mt-6">
            <button 
              onClick={handlePrevStep}
              className="bg-white border border-gray-200 hover:bg-gray-50 text-black w-10 h-10 rounded-full flex items-center justify-center"
            >
              <ArrowLeft size={18} />
            </button>
            <button 
              onClick={handleNextStep}
              className="bg-[#FE904B] hover:bg-[#fc873f] text-white w-10 h-10 rounded-full flex items-center justify-center"
            >
              <ArrowRight size={18} />
            </button>
          </div>
        </div>
          
          {/* Decorative flower image */}
          <div className="w-full h-full opacity-30">
            <Image 
              src={leafflower} 
              alt="Decorative flower" 
              className="object-contain"
            />
          </div>
        </div>
        
        {/* Right Section - Steps Carousel */}
        <div className="lg:w-[45%] w-[100%] flex flex-col justify-center items-center">
          {/* Step indicators */}
          <div className="w-[85%] flex justify-center mb-6 gap-[2rem] rounded-2xl p-2 items-center bg-[#f7f6f6]">
            {stepsData.map((step, index) => (
              <button
                key={index}
                onClick={() => setActiveStep(index)}
                className={`px-3 py-1 rounded-full text-xs ${
                  activeStep === index 
                    ? "bg-[#FE904B] text-white" 
                    : "bg-transparent text-gray-700 hover:bg-gray-100"
                }`}
              >
                Step {index + 1}
              </button>
            ))}
          </div>
          
          {/* Current step card */}
          <div className="bg-white w-[90%] rounded-lg p-0  md:p-6 shadow-sm">
            <div className="mb-4">
              <h3 className="text-2xl font-medium">{currentStep.id}</h3>
            </div>
            
            <div className="relative h-64 w-full mb-4 overflow-hidden rounded-md">
              <Image 
                src={currentStep.image}
                alt={currentStep.title}
                fill
                className="object-cover"
              />
            </div>
            
            <h4 className="text-xl font-medium mb-2">{currentStep.title}</h4>
            <p className="text-gray-600">{currentStep.description}</p>
            
           
          </div>
           {/* Progress indicator with percentage in circle */}
           <div className="mt-2 relative w-[80%]">
              <div className="bg-[#ffffff] h-8 p-1 rounded-full w-full">
                <div 
                  className="bg-[#fabacf] h-full rounded-full transition-all duration-500 ease-in-out"
                  style={{ width: `${progress}%` }}
                />
                <div 
                  className="absolute top-1/2 -translate-y-1/2 w-7 h-7 bg-[#FE904B] rounded-full flex items-center justify-center text-white text-[10px] font-medium transition-all duration-500 ease-in-out"
                  style={{ left: `calc(${progress}% - 16px)` }}
                >
                  {Math.round(progress)}%
                </div>
              </div>
            </div>
        </div>
      </div>
    </div>
  );
};

export default VisionHome;