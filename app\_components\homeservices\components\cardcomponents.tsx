import Image, { StaticImageData } from "next/image";
import Link from "next/link";
import tiltarrow from "@/public/image/icons/tilearrow.svg"

type CardProps = {
  number: string;
  title: string;
  description: string;
  image: StaticImageData;
};

export default function Card({ number, title, description, image }: CardProps) {
  return (
    <div className="relative w-full max-w-sm rounded-xl overflow-hidden shadow-md bg-white">
      {/* Image (No fill) */}
      <div className="relative">
        <Image
          src={image}
          alt={title}
          width={400}
          height={350}
          className="object-cover w-full h-[400px]"
        />

        {/* Top Left Number Badge */}
        <div className="absolute top-2 left-2 bg-white font-urbanist text-black w-8 h-8 rounded-full flex items-center justify-center text-xs font-thin shadow">
          {number}
        </div>

        {/* Top Right Icon */}
       <Link href="/gallary">
       <div className="absolute top-2 right-2 bg-orange-500 text-white w-8 h-8 rounded-full flex items-center justify-center shadow">
        <Image src={tiltarrow} alt="Arrow" width={30} height={31} className='p-2'/>
        </div>
        </Link>

        {/* Bottom Overlay */}
        <div className="absolute bottom-0 w-[95%] bg-white text-black px-3 rounded-lg text-left py-2 m-2 flex gap-y-1 flex-col items-start justify-center">
          <h3 className="text-sm font-semibold font-urbanist">{title}</h3>
          <p className="text-xs text-gray-600 font-urbanist">{description}</p>
        </div>
      </div>
    </div>
  );
}
