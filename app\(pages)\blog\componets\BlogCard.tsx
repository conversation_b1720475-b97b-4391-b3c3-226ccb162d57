"use client";
import React, { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { BsArrowRight } from "react-icons/bs";
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa";
import { formatBlogDate, BlogData } from "@/lib/api/blogs/blogApi";
import ShareModal from "./ShareModal";

interface BlogCardProps {
  blog: BlogData;
}

export default function BlogCard({ blog }: BlogCardProps) {
  const router = useRouter();
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);

  // Debug: Log the blog data to see what we're receiving
  console.log('BlogCard - blog data:', {
    id: blog._id,
    title: blog.title,
    createdAt: blog.createdAt,
    createdAtType: typeof blog.createdAt,
    formattedDate: blog.createdAt ? formatBlogDate(blog.createdAt) : 'No createdAt'
  });

  const handleBlogClick = () => {
    const slug = blog.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
    router.push(`/blog/${blog._id}/${slug}`);
  };

  const handleSocialShare = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsShareModalOpen(true);
  };

  return (
    <div
      onClick={handleBlogClick}
      className="bg-white border border-gray-200 shadow-lg hover:shadow-xl rounded-[16px] md:rounded-[20px] cursor-pointer p-0 flex flex-col relative overflow-hidden transition-all duration-300 group hover:scale-[1.02] md:hover:scale-105"
    >
      {/* Blog Image */}
      <div className="relative w-full h-[200px] sm:h-[250px] md:h-[300px] rounded-t-[16px] md:rounded-t-[20px] overflow-hidden">
        {blog.imageUrl ? (
          <Image
            src={blog.imageUrl}
            alt={blog.title}
            width={500}
            height={300}
            className="object-cover w-full h-full"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
            }}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-orange-500 to-orange-700 flex items-center justify-center">
            <div className="text-white text-center p-4">
              <div className="text-2xl md:text-3xl font-bold mb-2">📝</div>
              <div className="text-sm md:text-base font-medium">Blog Post</div>
            </div>
          </div>
        )}
        {/* Date - Top Left */}
        <div className="absolute top-3 left-3 md:top-4 md:left-4 bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 md:px-3 shadow-md">
          <span className="text-gray-900 text-xs md:text-sm font-medium">
            {blog.createdAt ? formatBlogDate(blog.createdAt) : "No date"}
          </span>
        </div>
        {/* Arrow Icon - Top Right */}
        <div className="absolute top-3 right-3 md:top-4 md:right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-white/20 backdrop-blur-sm rounded-full p-1 md:p-2">
            <BsArrowRight className="w-4 h-4 md:w-5 md:h-5 text-white" />
          </div>
        </div>
      </div>

      {/* Blog Content */}
      <div className="p-4 md:p-6 flex flex-col gap-3 md:gap-4 flex-1">
        {/* Category with Hover Dropdown */}
        <div className="relative group/category">
          <div className="flex items-center gap-2">
            <span className="bg-orange-500 text-white px-2 py-1 md:px-3 md:py-1 rounded-full text-xs md:text-sm">
              {blog.category[0]}
            </span>
            {blog.category.length > 1 && (
              <span className="text-white/50 text-xs md:text-sm cursor-pointer">
                +{blog.category.length - 1} more
              </span>
            )}
          </div>
          {/* Dropdown for additional categories */}
          {blog.category.length > 1 && (
            <div className="absolute top-full left-0 mt-1 md:mt-2 bg-zinc-800 rounded-lg p-2 md:p-3 opacity-0 group-hover/category:opacity-100 transition-opacity duration-300 z-10 min-w-[150px] md:min-w-[200px]">
              <div className="flex flex-wrap gap-1 md:gap-2">
                {blog.category.slice(1).map((cat, index) => (
                  <span
                    key={index}
                    className="bg-orange-500/80 text-white px-1 py-0.5 md:px-2 md:py-1 rounded-full text-[10px] md:text-xs"
                  >
                    {cat}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Title */}
        <h3 className="text-[16px] md:text-[20px] font-bold text-gray-900 leading-tight line-clamp-2">
          {blog.title}
        </h3>

        {/* Description */}
        <p className="text-gray-600 text-xs md:text-sm line-clamp-3">
          {blog.description}
        </p>

        {/* Bottom Section */}
        <div className="flex justify-between items-end mt-auto pt-3 md:pt-4">
          {/* Keywords - Only show first 2 */}
          <div className="flex flex-wrap gap-1 md:gap-2">
            {blog.keywords.slice(0, 2).map((keyword, index) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-700 px-1.5 py-0.5 md:px-2 md:py-1 rounded text-[10px] md:text-xs"
              >
                {keyword.replace(/[\[\]"]/g, "")}
              </span>
            ))}
          </div>

          {/* Social Share Icons */}
          <div className="flex gap-1 md:gap-2">
            <button
              onClick={handleSocialShare}
              className="p-1.5 md:p-2 bg-gray-100 hover:bg-blue-600 rounded-full transition-colors group"
            >
              <FaFacebook className="w-3 h-3 md:w-4 md:h-4 text-gray-600 group-hover:text-white" />
            </button>
            <button
              onClick={handleSocialShare}
              className="p-1.5 md:p-2 bg-gray-100 hover:bg-pink-600 rounded-full transition-colors group"
            >
              <FaInstagram className="w-3 h-3 md:w-4 md:h-4 text-gray-600 group-hover:text-white" />
            </button>
            <button
              onClick={handleSocialShare}
              className="p-1.5 md:p-2 bg-gray-100 hover:bg-blue-700 rounded-full transition-colors group"
            >
              <FaLinkedin className="w-3 h-3 md:w-4 md:h-4 text-gray-600 group-hover:text-white" />
            </button>
          </div>
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
        blogId={blog._id}
        blogTitle={blog.title}
      />
    </div>
  );
}
