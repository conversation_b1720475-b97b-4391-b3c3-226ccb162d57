// Comment API module
import { apiClient } from '../../customaxios';
import { buildUrl } from '../../globalurl';
import {
  Comment,
  CreateCommentRequest,
  UpdateCommentRequest,
  CommentResponse,
  CommentsResponse,
  CommentCountResponse,
  CommentQueryParams,
  CommentStatistics,
} from './types';

// Comment API functions
export const commentApi = {
  // Public APIs (No Authentication Required)

  // Create comment (Public)
  createComment: async (commentData: CreateCommentRequest): Promise<CommentResponse> => {
    const response = await apiClient.post<CommentResponse>(
      buildUrl('/comments'),
      commentData
    );
    return response.data;
  },

  // Get approved comments (Public)
  getApprovedComments: async (params: {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    limit?: number;
    blogId?: string | number;
  } = {}): Promise<CommentsResponse> => {
    const queryParams = new URLSearchParams();

    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.blogId) queryParams.append('blogId', params.blogId.toString());

    const url = `/comments/approved${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiClient.get<CommentsResponse>(buildUrl(url));
    return response.data;
  },

  // Get comments by blog ID (Public)
  getCommentsByBlogId: async (
    blogId: string | number,
    params: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      status?: string;
      isVisible?: boolean;
    } = {}
  ): Promise<CommentsResponse> => {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
    if (params.status) queryParams.append('status', params.status);
    if (params.isVisible !== undefined) queryParams.append('isVisible', params.isVisible.toString());

    const url = `/comments/blog/${blogId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiClient.get<CommentsResponse>(buildUrl(url));
    return response.data;
  },

  // Get comment count by blog ID (Public)
  getCommentCountByBlogId: async (blogId: string | number): Promise<CommentCountResponse> => {
    const response = await apiClient.get<CommentCountResponse>(
      buildUrl(`/comments/blog/${blogId}/count`)
    );
    return response.data;
  },

  // Admin APIs (Authentication Required)

  // Get all comments (Admin)
  getAllComments: async (params: CommentQueryParams = {}): Promise<CommentsResponse> => {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.status) queryParams.append('status', params.status);
    if (params.isVisible !== undefined) queryParams.append('isVisible', params.isVisible.toString());
    if (params.blogId) queryParams.append('blogId', params.blogId.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const url = `/comments${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiClient.get<CommentsResponse>(buildUrl(url));
    return response.data;
  },

  // Get comment by ID (Admin)
  getCommentById: async (id: string | number): Promise<CommentResponse> => {
    const response = await apiClient.get<CommentResponse>(buildUrl(`/comments/${id}`));
    return response.data;
  },

  // Update comment (Admin - Moderation)
  updateComment: async (
    id: string | number,
    updateData: UpdateCommentRequest
  ): Promise<CommentResponse> => {
    const response = await apiClient.put<CommentResponse>(
      buildUrl(`/comments/${id}`),
      updateData
    );
    return response.data;
  },

  // Delete comment (Admin)
  deleteComment: async (id: string | number): Promise<CommentResponse> => {
    const response = await apiClient.delete<CommentResponse>(buildUrl(`/comments/${id}`));
    return response.data;
  },

  // Get comment statistics (Admin)
  getCommentStatistics: async (): Promise<CommentStatistics> => {
    const response = await apiClient.get<CommentStatistics>(buildUrl('/comments/statistics'));
    return response.data;
  },

  // Moderation helper functions
  approveComment: async (id: string | number): Promise<CommentResponse> => {
    return commentApi.updateComment(id, { status: 'approved', isVisible: true });
  },

  rejectComment: async (id: string | number): Promise<CommentResponse> => {
    return commentApi.updateComment(id, { status: 'rejected', isVisible: false });
  },

  markAsSpam: async (id: string | number): Promise<CommentResponse> => {
    return commentApi.updateComment(id, { status: 'spam', isVisible: false });
  },

  // Get pending comments for moderation
  getPendingComments: async (params: {
    page?: number;
    limit?: number;
  } = {}): Promise<CommentsResponse> => {
    return commentApi.getAllComments({
      ...params,
      status: 'pending',
    });
  },
};

// Helper functions for existing components
export const createComment = async (commentData: CreateCommentRequest): Promise<CommentResponse> => {
  return commentApi.createComment(commentData);
};

export const getCommentsByBlog = async (
  blogId: string | number,
  page = 1,
  limit = 20
): Promise<CommentsResponse> => {
  return commentApi.getCommentsByBlogId(blogId, {
    page,
    limit,
    status: 'approved',
    isVisible: true,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });
};

export const getCommentCount = async (blogId: string | number): Promise<number> => {
  try {
    const response = await commentApi.getCommentCountByBlogId(blogId);
    return response.data.commentCount;
  } catch (error) {
    console.error('Error fetching comment count:', error);
    return 0;
  }
};

// Utility functions for comments
export const formatCommentDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    return 'Unknown';
  }
};

// Helper functions for existing components
export const submitComment = async (commentData: CreateCommentRequest): Promise<CommentResponse> => {
  return commentApi.createComment(commentData);
};

export const getApprovedComments = async (params: {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  blogId?: string | number;
}): Promise<CommentsResponse> => {
  return commentApi.getApprovedComments(params);
};

// Export types for components
export type { Comment, CreateCommentRequest } from './types';
