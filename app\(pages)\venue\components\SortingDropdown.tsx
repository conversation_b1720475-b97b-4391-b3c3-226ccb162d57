"use client";

import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface SortingDropdownProps {
  sortOption: string;
  onSortChange: (option: string) => void;
}

const SortingDropdown = ({ sortOption, onSortChange }: SortingDropdownProps) => {
  const [showSortOptions, setShowSortOptions] = useState(false);

  const handleSortChange = (option: string) => {
    onSortChange(option);
    setShowSortOptions(false);
  };

  return (
    <div className="relative">
      <button 
        className="flex items-center gap-2 text-sm border border-gray-300 rounded px-3 py-1.5"
        onClick={() => setShowSortOptions(!showSortOptions)}
      >
        {sortOption === "default" ? "Default sorting" : 
         sortOption === "price-low-high" ? "Price: Low to High" :
         sortOption === "price-high-low" ? "Price: High to Low" :
         sortOption === "capacity-low-high" ? "Capacity: Low to High" :
         "Capacity: High to Low"}
        <ChevronDown size={16} />
      </button>
      
      {showSortOptions && (
        <div className="absolute right-0 mt-1 w-48 bg-white border border-gray-200 rounded shadow-lg z-10">
          <ul>
            <li 
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSortChange("default")}
            >
              Default sorting
            </li>
            <li 
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSortChange("price-low-high")}
            >
              Price: Low to High
            </li>
            <li 
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSortChange("price-high-low")}
            >
              Price: High to Low
            </li>
            <li 
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSortChange("capacity-low-high")}
            >
              Capacity: Low to High
            </li>
            <li 
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
              onClick={() => handleSortChange("capacity-high-low")}
            >
              Capacity: High to Low
            </li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default SortingDropdown;