{"name": "parv-event-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@material-tailwind/react": "^1.4.2", "@mui/material": "^7.0.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.503.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "react-transition-group": "^4.4.5", "tailwind-merge": "^3.2.0", "yet-another-react-lightbox": "^3.23.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tw-animate-css": "^1.2.8", "typescript": "^5"}}