"use client";
import React, { useState, useEffect } from "react";
import {
  getAllBlogs,
  getBlogCategories,
  getRecentBlogs,
  BlogData,
} from "@/lib/api/blogs/blogApi";
import BlogCard from "./BlogCard";
import BlogSidebar from "./BlogSidebar";
import BlogPagination from "./BlogPagination";

export default function BlogCards() {
  const [blogs, setBlogs] = useState<BlogData[]>([]);
  const [categories, setCategories] = useState<
    Array<{ name: string; count: number }>
  >([]);
  const [recentBlogs, setRecentBlogs] = useState<BlogData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Fetch blogs data
  useEffect(() => {
    fetchBlogs();
    fetchCategories();
    fetchRecentBlogs();
  }, [currentPage, searchQuery, selectedCategory]);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      const response = await getAllBlogs({
        page: currentPage,
        pageSize: 6,
        search: searchQuery || undefined,
        category: selectedCategory || undefined,
      });

      if (response && response.status && response.data) {
        setBlogs(response.data.data || []);
        setTotalPages(response.data.totalPages || 1);
      } else {
        setBlogs([]);
        setTotalPages(1);
      }
    } catch (error) {
      console.error("Error fetching blogs:", error);
      setBlogs([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await getBlogCategories();

      if (response && response.status && response.data) {
        setCategories(response.data);
      } else {
        setCategories([]);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      setCategories([]);
    }
  };

  const fetchRecentBlogs = async () => {
    try {
      const response = await getRecentBlogs(3);
      if (response.status) {
        setRecentBlogs(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching recent blogs:", error);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchBlogs();
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category === selectedCategory ? "" : category);
    setCurrentPage(1);
  };

  const handleKeywordClick = (keyword: string) => {
    setSearchQuery(keyword.replace(/[\[\]"]/g, ""));
    setCurrentPage(1);
    fetchBlogs();
  };

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Get all unique keywords from blogs
  const allKeywords = blogs
    .flatMap((blog) => blog.keywords)
    .filter((keyword, index, arr) => arr.indexOf(keyword) === index);

  return (
    <div className="text-gray-900 w-full flex justify-center py-10 md:py-20 bg-white">
      <div className="w-[95%] max-w-[1400px] flex flex-col lg:flex-row gap-8">
        {/* Mobile Sidebar Toggle */}
        <div className="lg:hidden flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Blog Posts</h1>
          <button
            onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg"
          >
            {isMobileSidebarOpen ? "Hide Filters" : "Show Filters"}
          </button>
        </div>

        {/* Mobile Sidebar */}
        {isMobileSidebarOpen && (
          <div className="lg:hidden w-full mb-8">
            <BlogSidebar
              categories={categories}
              recentBlogs={recentBlogs}
              allKeywords={allKeywords}
              searchQuery={searchQuery}
              selectedCategory={selectedCategory}
              onSearchChange={handleSearchChange}
              onSearchSubmit={handleSearch}
              onCategoryFilter={handleCategoryFilter}
              onKeywordClick={handleKeywordClick}
            />
          </div>
        )}

        {/* Left Side - Blog Cards */}
        <div className="flex-1">
          {loading ? (
            <div className="text-center py-20">
              <div className="text-gray-900">Loading blogs...</div>
            </div>
          ) : (
            <>
              {/* Blog Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6 md:gap-8 mb-8 md:mb-16">
                {blogs.map((blog) => (
                  <BlogCard key={blog._id} blog={blog} />
                ))}
              </div>

              {/* Pagination */}
              {blogs.length > 0 && (
                <BlogPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              )}
            </>
          )}
        </div>

        {/* Right Sidebar - Desktop */}
        <div className="hidden lg:block w-[350px]">
          <BlogSidebar
            categories={categories}
            recentBlogs={recentBlogs}
            allKeywords={allKeywords}
            searchQuery={searchQuery}
            selectedCategory={selectedCategory}
            onSearchChange={handleSearchChange}
            onSearchSubmit={handleSearch}
            onCategoryFilter={handleCategoryFilter}
            onKeywordClick={handleKeywordClick}
          />
        </div>
      </div>
    </div>
  );
}
