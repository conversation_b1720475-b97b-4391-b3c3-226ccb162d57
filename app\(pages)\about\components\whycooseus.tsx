"use client";
import React from 'react';
import Image from 'next/image';
import topleftflower from "@/public/image/icons/about/topleftflower.png";
import bottomrightflower from "@/public/image/assests/aboutflower.png";
import managmenticon from "@/public/image/icons/eventmanagement.svg";
import venueicons from "@/public/image/icons/venue.svg";
import supporticon from "@/public/image/icons/support.svg";
import eventImage from "@/public/image/icons/about/aboutus_eventplanning.png";
import arrowfull from "@/public/image/icons/about/arrowfull.svg";
import rosrgift3rd from "@/public/image/assests/rosrgift3rd.png"

const WhyCooseUs = () => {
  return (
    <div className="py-16 pt-24 relative bg-[#FFEDE3]">
      {/* Top left flower decoration */}
      <div className="absolute top-0 left-0 w-40 z-20 h-40 opacity-50 md:w-64 md:h-64 pointer-events-none">
        <Image 
          src={topleftflower} 
          alt="Decorative flower" 
          width={256}
          height={256}
          className="object-contain"
        />
      </div>
      
      {/* Bottom right flower decoration */}
      <div className="absolute bottom-0 right-0 w-40 h-40 md:w-64 md:h-64 pointer-events-none">
        <Image 
          src={bottomrightflower} 
          alt="Decorative flower" 
          fill
          className="object-contain"
        />
      </div>
      
      <div className="max-w-5xl mx-auto relative z-10 px-4 py-14">
        <div className="grid grid-cols-1 lg:grid-cols-2 items-center">
          {/* Left side - Services */}
          <div className="space-y-2">
            <div className="text-left">
              <div className='flex flex-row items-center'>
                <p className="text-[15px] text-[#C28565]">Why Choose Us</p>
                <Image src={arrowfull} alt="Arrow" width={105} height={75}/>
              </div>
            </div>
            
            <h2 className="text-2xl sm:text-2xl md:text-[30px] font-medium text-left leading-tight">
              We Have 25 Years Of Experience <br className="hidden sm:block"/>
              With Event Planning.
            </h2>
            
            <p className="text-left text-gray-700 max-w-3xl text-[12px]">
              We prioritize creating seamless and memorable events, ensuring a well-organized and 
              enjoyable experience for all attendees. Our team follows meticulous planning and 
              execution strategies to bring your vision to life.
            </p>
            
            {/* Event Planning & Management */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <Image 
                  src={managmenticon} 
                  alt="Event Planning" 
                  width={50} 
                  height={50}
                />
              </div>
              <div>
                <h3 className="text-[18px] font-medium mb-2">Event Planning & Management</h3>
                <p className="text-gray-700 text-[12px]">
                  From intimate gatherings to corporate events to large-scale celebrations, 
                  we handle every detail to ensure your event runs smoothly from start to finish.
                </p>
              </div>
            </div>
            
            {/* Perfect Venue Selection */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <Image 
                  src={venueicons} 
                  alt="Venue Selection" 
                  width={50} 
                  height={50}
                />
              </div>
              <div>
                <h3 className="text-[18px] font-medium mb-2">Perfect Venue Selection</h3>
                <p className="text-gray-700 text-[12px]">
                  That's why we carefully choose premium venues and top-notch 
                  vendors to ensure a flawless event experience.
                </p>
              </div>
            </div>
            
            {/* Dedicated Support */}
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <Image 
                  src={supporticon} 
                  alt="Dedicated Support" 
                  width={50} 
                  height={50}
                />
              </div>
              <div>
                <h3 className="text-[18px] font-medium mb-2">Dedicated Support</h3>
                <p className="text-gray-700 text-[12px]">
                  Our team provides round-the-clock assistance, handling every detail 
                  so you can enjoy a stress-free and memorable event.
                </p>
              </div>
            </div>
          </div>
          
          {/* Right side - Image */}
          <div className="relative h-[350px] sm:h-[400px] md:h-[450px] rounded-lg flex items-center justify-center mt-8 lg:mt-0">
            <Image 
              src={eventImage} 
              alt="Event Planning" 
              width={500}
              height={500}
              className="object-cover w-[90%] sm:w-[85%] md:w-[80%] h-[100%] rounded-lg"
            />
            <Image 
              src={rosrgift3rd} 
              alt="rosrgift3rd" 
              width={100} 
              height={100} 
              className="absolute -bottom-10 -left-5 sm:-bottom-15 sm:-left-5 md:-bottom-20 md:-left-5 w-[100px] sm:w-[120px] md:w-[150px] h-auto"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhyCooseUs
