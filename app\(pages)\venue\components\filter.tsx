"use client";
import React, { useState, useEffect } from 'react';
import { VenueType, LocationType } from '../venualldummydata/venue_dummydata';

interface FilterProps {
  onFilterChange: (filters: {
    searchTerm: string;
    venueTypes: VenueType[];
    locations: string[];
  }) => void;
  availableVenueTypes: VenueType[];
  availableLocations: string[];
}

const Filter: React.FC<FilterProps> = ({ onFilterChange, availableVenueTypes, availableLocations }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [venueTypes, setVenueTypes] = useState<VenueType[]>([]);
  const [locations, setLocations] = useState<string[]>([]);
  const [isOpen, setIsOpen] = useState(true);
  const [filtersChanged, setFiltersChanged] = useState(false);

  // Helper function to format venue type for display
  const formatVenueTypeDisplay = (type: VenueType): string => {
    const typeMap: Record<VenueType, string> = {
      'banquet-hall': 'Banquet Hall',
      'outdoor': 'Outdoor',
      'resort': 'Resort',
      'hotel': 'Hotel',
      'farmhouse': 'Farm House',
      'palace': 'Palace',
      'garden': 'Garden',
      'beach': 'Beach',
      'other': 'Other'
    };
    return typeMap[type] || type;
  };

  const handleVenueTypeChange = (type: VenueType) => {
    if (venueTypes.includes(type)) {
      setVenueTypes(venueTypes.filter(t => t !== type));
    } else {
      setVenueTypes([...venueTypes, type]);
    }
    setFiltersChanged(true);
  };

  const handleLocationChange = (location: string) => {
    if (locations.includes(location)) {
      setLocations(locations.filter(l => l !== location));
    } else {
      setLocations([...locations, location]);
    }
    setFiltersChanged(true);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setFiltersChanged(true);
  };

  // Apply filters only when they've changed and after a short delay
  useEffect(() => {
    if (!filtersChanged) return;

    const timer = setTimeout(() => {
      onFilterChange({
        searchTerm,
        venueTypes,
        locations
      });
      setFiltersChanged(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, venueTypes, locations, filtersChanged, onFilterChange]);

  return (
    <div className="bg-[#FEF2EB] p-4 rounded-lg">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-medium text-lg">Filter Search</h3>
        <button 
          onClick={() => setIsOpen(!isOpen)} 
          className="md:hidden text-sm text-gray-500 hover:text-gray-700"
        >
          {isOpen ? 'Close' : 'Open'}
        </button>
      </div>

      {/* Always show on md+ screens, toggle on mobile based on isOpen */}
      <div className={`${isOpen ? 'block' : 'hidden'} md:block`}>
        {/* Event Search */}
        <div className="mb-6">
          <h4 className="font-medium mb-2">Event</h4>
          <input
            type="text"
            placeholder="Search"
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#FE904B]"
          />
        </div>

        {/* Venue Type */}
        <div className="mb-6">
          <h4 className="font-medium mb-2">Venue Type</h4>
          <div className="space-y-2">
            {availableVenueTypes.map((type) => (
              <div key={type} className="flex items-center">
                <input
                  type="checkbox"
                  id={type}
                  checked={venueTypes.includes(type)}
                  onChange={() => handleVenueTypeChange(type)}
                  className="mr-2 h-4 w-4 accent-[#FE904B]"
                />
                <label htmlFor={type}>{formatVenueTypeDisplay(type)}</label>
              </div>
            ))}
            {availableVenueTypes.length === 0 && (
              <p className="text-gray-500 text-sm">No venue types available</p>
            )}
          </div>
        </div>

        {/* Location */}
        <div className="mb-6">
          <h4 className="font-medium mb-2">Location</h4>
          <div className="space-y-2">
            {availableLocations.map((location) => (
              <div key={location} className="flex items-center">
                <input
                  type="checkbox"
                  id={location.toLowerCase().replace(/\s+/g, '-')}
                  checked={locations.includes(location)}
                  onChange={() => handleLocationChange(location)}
                  className="mr-2 h-4 w-4 accent-[#FE904B]"
                />
                <label htmlFor={location.toLowerCase().replace(/\s+/g, '-')}>{location}</label>
              </div>
            ))}
            {availableLocations.length === 0 && (
              <p className="text-gray-500 text-sm">No locations available</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Filter
